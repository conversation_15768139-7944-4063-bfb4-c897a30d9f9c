# Notification group names
# These define the categories for different types of notifications
notification.group.toco=Toco Notifications

# Settings dialog titles and tabs
# Labels for the settings dialog and its tabs
settings.title=Toco Design Settings
settings.tab.application=Application Settings
settings.tab.project=Project Settings
settings.tab.indexing=Indexing

# Account related labels
# Text for account-related UI elements
account.title=Account
account.not.logged.in=Not logged in
account.login=Login
account.logout=Logout

# Indexing setting panel
indexing.codebase.title=Codebase Indexing
indexing.codebase.labelSuccess=files synced
indexing.codebase.button.start=Compute Index
indexing.codebase.button.sync=Sync
indexing.codebase.button.delete=Delete Index
indexing.codebase.button.pause=Pause Indexing
indexing.codebase.button.continue=Continue
indexing.codebase.tip=Codebase indexing analyzes your project structure to create intelligent indices that help LLM find code quickly, understand dependencies, and improve development efficiency. Your code is only used for indexing computation and will not be permanently stored on our servers. The index data is stored on the server, but your source code is not saved. All processing follows strict privacy standards.

# General settings
# Labels for general application settings
general.settings.title=General settings
host.label=Host:
frontend.host.label=Frontend Host:
language.label=Display language:
inline.completion.label=Inline completion
online.web.label=OnlineWeb
locate.app.shortcut.label=Locate app shortcut:
locate.app.shortcut.placeholder=Press the desired key combination

# Privacy and legal
# Labels for privacy and legal information
privacy.statement.title=Privacy statement
service.agreement.label=Service Agreement
privacy.policy.label=Privacy Policy

# Button labels
button.clear=Clear
button.save=Save
button.cancel=Cancel
button.ok=Ok
button.no=No
button.create=Create

# Error messages
# Messages displayed when errors occur
message.error.host.empty=Host cannot be empty
message.error.host.invalid=Invalid host URL
message.error.host.unreachable=The host configuration is incorrect, or the login URL is unreachable.

# Informational messages
# Messages providing information to the user
message.language.change.restart=You need to reopen the settings page to apply the new language.
title.settings.changed=Settings changed
message.settings.not.applied=Settings have not been applied. Do you want to apply them now?

message.editor.modified.confirm.title=This tab has been modified
message.project.modified.confirm.title=Some tab(s) has been modified
message.editor.modified.confirm.message=Are your sure to close it?

# Login dialog
# Title for the login dialog
login.dialog.title=Login

# Notifications
# Messages for various notification scenarios
notification.login.failed=Failed to login, please retry
notification.logout.failed=Failed to logout, please retry
notification.login.expired.title=TocoDesign Login Expired
notification.login.expired=Your login has expired. Please log in again.

# Actions
# Labels for action items
action.open.settings=Open Settings

# Locator-related messages
# Messages and titles related to the locator functionality
locator.start.fail.title=Locator Start Failed
locator.start.fail.message.project.info=Project information is missing. Unable to start locator.
locator.start.fail.message.socket=Failed to initialize socket connection. Please try again later.
locator.title=Toco Locator
locator.login.required=Please log in before locating.
locator.activate.online=Activate Online
locator.offline.message=The client is currently offline. Click to activate online status.
locator.no.desktop.app=Please open and log in to the TocoDesign desktop application, then open project: {0}
locator.invalid.message.body=Invalid locator request message: missing locator information
locator.unsupported.type=Invalid locator request message: unsupported locator type {0}
locator.unsupported.sub.type=Invalid locator request message: unsupported sub locator type {0}
locator.missing.field=Invalid locator request message: missing required field in locator information
locator.failed.to.open=Location failed: Code might not be generated, remote code not synchronized, or Maven modules not loaded.
locator.success=Locator success, please check the IDE
open.tocodesign.tab=Open in TocoDesign Tab
locator.file.not.found=Locator: File not found in the project.
locator.cannot.open.file=Locator: Cannot open the specified file in the editor.
locator.partial.failure=Locator: Partial failure in highlighting lines: {0}.
locator.highlight.success=Locator: Lines highlighted successfully.
locator.settings.title=Locator Settings
highlight.background.color=Highlight Background Color:
choose.highlight.color=Choose Highlight Color
reset.color=Reset to Default
highlight.color.instruction=Click the button to change highlight color

# Project configuration related messages
# Labels and messages for project configuration
button.check=Recheck Project
project.id.label=Project ID:
project.name.label=Project Name:
error.config.not.found=Configuration file not found.
error.invalid.config=Invalid configuration. Project ID or Name is missing.
success.config.updated=Configuration updated successfully

# Notification messages for configuration-related issues
notification.error.project.root.not.found.title=Project Root Not Found
notification.error.project.root.not.found.message=Unable to determine project root directory
notification.error.config.not.found.title=Configuration Not Found
notification.error.config.not.found.message=Unable to find TocoDesign project file: {0}
notification.warning.config.read.error.title=Configuration Error
notification.warning.config.read.error.message=Error reading TocoDesign project file: {0}

# Project configuration description
project.config.description=TocoDesign project root must have a 'project' config file with id and name.

# Status bar widget and menu items
# Labels and messages for the status bar widget and its context menu
statusbar.widget.display.name=Toco Design
statusbar.menu.title=Toco Design
statusbar.widget.tooltip=Toco Design Status
statusbar.menu.open.setting=Open Setting
statusbar.menu.login.status.logged_in=Logged in ({0})
statusbar.menu.login.status.not_logged_in=Not logged in
statusbar.menu.locator.status=Locator connection: {0}
statusbar.menu.locator.status.disconnected=Disconnected
statusbar.menu.locator.status.connected=Unpaired(Connected)
statusbar.menu.locator.status.paired=Paired

# Notification messages for JCef-related issues
notification.jcef.unavailable.title=JCef Unavailable
notification.jcef.unavailable.content=The current version of IntelliJ IDEA does not support JCef. Please upgrade to a newer version.

# JDK compatibility check related labels
jdk.version.label=JDK Compatibility Version:
button.check.jdk.compatibility=Check
error.jdk.version.empty=Please enter a JDK version.
success.jdk.compatibility=No incompatible items found. The project is compatible with the specified JDK version.
dialog.incompatible.items.title=Incompatible Items Found
error.title=Error
success.title=Success
jdk.version.unsupported.message=The input JDK version is not supported. Supported JDK versions range from 1.8 to 21.
jdk.version.unsupported.title=Unsupported JDK Version

# Dialog related labels
dialog.button.close=Close

# Key for toggling the display of bubble messages
toggle.bubble.messages.current.project=Display bubble messages for the current project (effective immediately)
toggle.bubble.messages=Display bubble messages

# New Project Wizard
new.project.wizard.project.name.empty=Project name should be set
new.project.wizard.location.empty=Location should be set
new.project.wizard.group.name=Group Name
new.project.wizard.database=Database
new.project.wizard.org=Organization
new.project.wizard.org.loading=Loading...
new.project.wizard.org.load.failed=Load organization failed
new.project.wizard.description=Description
new.project.wizard.git.repo=Git Repo
new.project.wizard.database.empty=Database should be set
new.project.wizard.group.name.empty=Group Name should be set
new.project.wizard.create.project.failed=Create project failed
new.project.wizard.generate.file=Generating project files

# code generate
code.generate.start=Generating code
code.generate.success=Code generated successfully for module {0}
code.generate.failed=Code generation failed for module {0}
code.generate.failed2=Code generation failed
code.generate.cancelled=Code generation cancelled for module {0}
code.generate.resolve.loading=Loading files...
code.generate.resolve.conflict=Resolve conflict
code.generate.resolve.conflict.title=Conflict
code.generate.resolve.conflict.description=Resolve the conflict before proceeding.
code.generate.resolve.recover=Recover files
code.generate.resolve.recover.title=Recoverable
code.generate.resolve.recover.description=Recoverable file list
code.generate.resolve.recover.button=Recover
code.generate.recover.do.not.show=Do not show for this module
code.generate.ours=Local
code.generate.theirs=Generated
code.generate.base=Base
code.generate.fetching.code=Fetching module code
code.generate.module.config=Fetching module config
code.generate.create.module=Creating module
code.generate.generating=Generating module code
code.generate.clean.module=Cleaning module code
code.generate.write.module.code=Writing module code
code.generate.commit.module.code=Committing module code
code.generate.merge.module.code=Merging module code
code.generate.diff.module.code=Diffing module code
code.generate.recover.title=Some files are deleted
code.generate.recover.message=Need to recover?
code.apply=Apply code
code.apply.all.finished=Apply code finished
code.apply.failed=Code apply failed
code.apply.failed.file.not.exist=File not exist
code.apply.accept.all=Apply All
code.apply.accept.all.description=Apply all changes
code.apply.reject.all=Reject All
code.apply.reject.all.description=Reject all changes
code.diff.current.version = Current version
code.diff.origin.version = Origin version
code.diff.title.file.diff = File Diff

copy.file.url.success=Copy Toco File Url
copy.file.url.success.description=File URL copied to clipboard Success